# This is a temporary file to show the changes needed

Key changes to implement in Chat.vue:

1. Add state management:
```js
// Add at the top with other refs
const conversationState = ref({
  mode: 'welcome',
  topic: null,
  step: 'initial',
  lastValidState: null
});

// Add after component mounting
onMounted(() => {
  // Try to restore state from localStorage
  const savedState = localStorage.getItem('conversationState');
  if (savedState) {
    conversationState.value = JSON.parse(savedState);
  }
});
```

2. Modify sendMessage function:
```js
async function sendMessage() {
  if (!isConnected.value) {
    notify("Please connect your API key first", "error");
    return;
  }

  const message = messageInput.value.trim();
  if (!message) return;

  // Update conversation state based on user input
  const prevState = { ...conversationState.value };
  
  if (message.toLowerCase() === 'menu') {
    conversationState.value.mode = 'menu';
    conversationState.value.step = 'option_selection';
  } else if (['1', '2', '3'].includes(message)) {
    const modes = ['brainstorm', 'review', 'feedback'];
    conversationState.value.mode = modes[parseInt(message) - 1];
    conversationState.value.step = conversationState.value.mode === 'brainstorm' ? 'topic_selection' : 'initial';
  } else if (conversationState.value.mode === 'brainstorm' && conversationState.value.step === 'topic_selection') {
    conversationState.value.topic = message;
    conversationState.value.step = 'brainstorming';
  }

  // Store last valid state
  conversationState.value.lastValidState = prevState;

  // Save state to localStorage
  localStorage.setItem('conversationState', JSON.stringify(conversationState.value));

  chatHistory.value.push({
    role: "user",
    content: message,
    timestamp: new Date(),
  });

  messageInput.value = "";
  if (textareaRef.value) {
    textareaRef.value.style.height = 'auto';
  }

  chatHistory.value.push({
    role: "assistant",
    content: "⏳ Assistant is typing...",
    timestamp: new Date(),
    typing: true,
  });

  try {
    const messagesToSend = chatHistory.value
      .filter(m => !m.typing)
      .map(m => ({ role: m.role, content: m.content }));

    const response = await fetch(
      `${import.meta.env.VITE_API_BASE_URL}/api/chat`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          messages: messagesToSend,
          apiKey: apiKey.value,
          provider: "hkbu",
          model: model.value,
          systemPrompt: systemPrompt.value,
          conversationContext: {
            mode: conversationState.value.mode,
            step: conversationState.value.step,
            topic: conversationState.value.topic
          }
        }),
      }
    );

    chatHistory.value = chatHistory.value.filter((m) => !m.typing);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response.' }));
      throw new Error(errorData.error || `Request failed with status ${response.status}`);
    }

    const data = await response.json();

    if (data.response) {
      chatHistory.value.push({
        role: "assistant",
        content: data.response,
        timestamp: new Date(),
      });
    } else {
      throw new Error(data.error || "Received an empty response from the server.");
    }
  } catch (error) {
    chatHistory.value = chatHistory.value.filter((m) => !m.typing);
    chatHistory.value.push({
      role: "assistant",
      content: `⚠️ Error: ${error.message}`,
      timestamp: new Date(),
    });

    // Restore previous state on error
    conversationState.value = conversationState.value.lastValidState || {
      mode: 'welcome',
      topic: null,
      step: 'initial',
      lastValidState: null
    };
  }
}
```

3. Add state reset in startNewSession:
```js
function startNewSession() {
  chatHistory.value = [];
  // Reset conversation state
  conversationState.value = {
    mode: 'welcome',
    topic: null,
    step: 'initial',
    lastValidState: null
  };
  localStorage.removeItem('conversationState');
  
  if (isConnected.value) {
    chatHistory.value.push({
      role: "assistant",
      content: welcomePrompt.value,
      timestamp: new Date(),
    });
  }
  notify("Started new session", "success");
}
```
