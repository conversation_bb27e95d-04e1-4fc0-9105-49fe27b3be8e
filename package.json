{"name": "new-bytewise", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "serve -s dist -p $PORT"}, "dependencies": {"jspdf": "^3.0.2", "pinia": "^3.0.3", "serve": "^14.2.4", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/vite": "^4.1.12", "@vitejs/plugin-vue": "^6.0.1", "tailwindcss": "^4.1.12", "vite": "^7.1.2"}}